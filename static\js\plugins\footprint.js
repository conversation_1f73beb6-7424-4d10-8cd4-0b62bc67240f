// --- High Performance Footprint Chart Plugin ---
import { positionsLine, positionsBox, drawRounded<PERSON>andle, isValidOHLCData } from './chart-utils.js';

/**
 * High-performance footprint renderer with optimized drawing and text caching
 */
class FootprintRenderer {
    constructor() {
        this._data = null;
        this._options = null;
        this._textCache = new Map();
        this._font10 = '10px -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif';
        this._font9 = '9px -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif';
        this._maxCacheSize = 200; // Increased cache size for better performance
        this._persistentRectangles = []; // Store persistent imbalance rectangles
        this._lastDataLength = 0; // Track data length for change detection
    }

    draw(target, priceToCoordinate) {
        target.useBitmapCoordinateSpace(scope => this._drawImpl(scope, priceToCoordinate));
    }

    update(data, options) {
        this._data = data;
        this._options = options;

        // Clear persistent rectangles when data is completely refreshed
        if (data && data.bars && this._persistentRectangles.length > 0) {
            // Only clear if this looks like a complete data refresh (not just an update)
            const hasSignificantChange = !this._lastDataLength ||
                Math.abs(data.bars.length - this._lastDataLength) > 1;
            if (hasSignificantChange) {
                this._persistentRectangles = [];
            }
            this._lastDataLength = data.bars.length;
        }

        // Manage cache size for memory efficiency - only clear when cache gets too large
        // to prevent unnecessary clearing that causes blinking
        if (this._textCache.size > this._maxCacheSize) {
            // Clear only half the cache instead of clearing everything
            const entries = Array.from(this._textCache.entries());
            const toKeep = entries.slice(-Math.floor(this._maxCacheSize / 2));
            this._textCache.clear();
            toKeep.forEach(([key, value]) => this._textCache.set(key, value));
        }
    }

    _drawImpl(scope, priceToCoordinate) {
        const d = this._data;
        if (!d?.bars?.length || !d.visibleRange || !this._options) return;

        const ctx = scope.context;
        const { from, to } = d.visibleRange;
        const barSpacing = d.barSpacing;

        // Use simple candlesticks for very small spacing
        if (barSpacing < 6) {
            this._drawCandles(ctx, priceToCoordinate, scope, from, to);
            return;
        }

        // Process all bars to update persistent rectangles
        this._updatePersistentRectangles(d.bars, priceToCoordinate);

        ctx.save();
        ctx.imageSmoothingEnabled = false;

        try {
            // Draw persistent rectangles first (behind everything)
            this._drawPersistentRectangles(ctx, priceToCoordinate, scope, from, to);

            for (let i = from; i < to; ++i) {
                const bar = d.bars[i];
                if (bar?.originalData && isValidOHLCData(bar.originalData)) {
                    this._drawBar(ctx, bar, priceToCoordinate, scope, barSpacing);
                }
            }
        } finally {
            ctx.restore();
        }
    }

    _drawCandles(ctx, priceToCoordinate, scope, from, to) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        
        for (let i = from; i < to; ++i) {
            const bar = this._data.bars[i];
            if (!bar?.originalData || !isValidOHLCData(bar.originalData)) continue;
            
            const d = bar.originalData;
            const x = bar.x;
            const openY = priceToCoordinate(d.open);
            const closeY = priceToCoordinate(d.close);
            const highY = priceToCoordinate(d.high);
            const lowY = priceToCoordinate(d.low);
            
            if ([openY, closeY, highY, lowY].some(Number.isNaN)) continue;
            
            const isUp = d.close >= d.open;
            ctx.fillStyle = isUp ? this._options.upColor : this._options.downColor;
            ctx.fillRect(Math.round(x * h), Math.min(openY, closeY) * v, 1, Math.abs(closeY - openY) * v || 1);
            
            if (this._options.wickVisible) {
                ctx.fillStyle = isUp ? this._options.wickUpColor : this._options.wickDownColor;
                ctx.fillRect(Math.round(x * h), Math.min(highY, lowY) * v, 1, Math.abs(lowY - highY) * v || 1);
            }
        }
    }

    _drawBar(ctx, bar, priceToCoordinate, scope, barSpacing) {
        const { originalData: d, x } = bar;
        const bodyW = Math.max(2, Math.min(12, barSpacing * 0.8));
        
        this._drawCandle(ctx, d, x, bodyW, priceToCoordinate, scope);
        
        if (barSpacing >= 18 && Array.isArray(d.footprint) && d.footprint.length) {
            const fpW = this._footprintWidth(ctx, d.footprint);
            this._drawCells(ctx, d, x + bodyW / 2 + 4, fpW, priceToCoordinate, scope);
        }
    }

    _drawCandle(ctx, d, x, w, priceToCoordinate, scope) {
        drawRoundedCandle(ctx, d, x, w, priceToCoordinate, scope, this._options);
    }

    _footprintWidth(ctx, footprint) {
        const key = footprint.map(f => `${f.buyVolume}x${f.sellVolume}`).join('|');
        if (this._textCache.has(key)) return this._textCache.get(key);
        
        ctx.font = this._font10;
        let maxW = 0;
        
        for (const f of footprint) {
            const txt = this._formatText(f.buyVolume, f.sellVolume, f.buyVolume - f.sellVolume);
            maxW = Math.max(maxW, ctx.measureText(txt).width);
        }
        
        const fpW = Math.max(36, Math.ceil(maxW) + 10);
        this._textCache.set(key, fpW);
        return fpW;
    }

    _drawCells(ctx, d, startX, width, priceToCoordinate, scope) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        const fp = Array.isArray(d.footprint) ? [...d.footprint].sort((a, b) => b.priceLevel - a.priceLevel) : [];
        if (!fp.length) return;
        const highY = priceToCoordinate(d.high);
        const lowY = priceToCoordinate(d.low);
        const range = Math.abs(lowY - highY);
        const levels = fp.length;
        const cellH = range / levels;

        ctx.font = this._font9;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // Gradient color stops
        const downGradient = this._options.downGradientColors || [
            '#F08090', '#D94A5A', '#A12A3A', '#3B0D16'
        ];
        const upGradient = this._options.upGradientColors || [
            '#5FE1C5', '#2FC1A2', '#007C65', '#00332A'
        ];
        const neutralColor = this._options.neutralColor || '#B2B5BE';

        const volumes = fp.map(f => (f.buyVolume || 0) + (f.sellVolume || 0));
        const maxVol = Math.max(...volumes);
        const minVol = Math.min(...volumes);
        const volRange = maxVol - minVol || 1;

        const cellGeometry = new Map();
        const xPosStatic = positionsLine(startX + width / 2, h, width);

        for (let i = 0; i < levels; ++i) {
            const f = fp[i];
            const cellTop = Math.round((highY + i * cellH) * v) / v;
            const cellBottom = Math.round((highY + (i + 1) * cellH) * v) / v;
            if ([cellTop, cellBottom].some(Number.isNaN)) continue;
            const buy = f.buyVolume || 0;
            const sell = f.sellVolume || 0;
            const total = buy + sell;
            const delta = buy - sell;
            const xPos = xPosStatic;
            const yPos = positionsBox(cellTop, cellBottom, v);
            let bg, alpha;
            if (!total) {
                bg = neutralColor;
                alpha = 0.08;
            } else {
                const ratio = (total - minVol) / volRange;
                const grad = delta >= 0 ? upGradient : downGradient;
                const idx = Math.min(grad.length - 1, Math.floor(ratio * grad.length));
                bg = grad[idx];
                alpha = Math.min(0.7, 0.25 + Math.min(1, Math.abs(delta / (total || 1))) * 0.45);
            }
            ctx.globalAlpha = alpha;
            ctx.fillStyle = bg;
            const radius = Math.min(2, Math.min(xPos.length, yPos.length) * 0.1);
            if (radius > 0.5 && (i === 0 || i === levels - 1)) {
                ctx.beginPath();
                if (i === 0) {
                    ctx.roundRect(xPos.position, yPos.position, xPos.length, yPos.length, [radius, radius, 0, 0]);
                } else {
                    ctx.roundRect(xPos.position, yPos.position, xPos.length, yPos.length, [0, 0, radius, radius]);
                }
                ctx.fill();
            } else {
                ctx.fillRect(xPos.position, yPos.position, xPos.length, yPos.length);
            }
            if (xPos.length >= 18 && yPos.length >= 8) {
                ctx.globalAlpha = 1;
                ctx.fillStyle = this._options.textColor || '#fff';
                ctx.fillText(
                    this._formatText(buy, sell, delta), 
                    xPos.position + xPos.length / 2, 
                    yPos.position + yPos.length / 2
                );
            }
            cellGeometry.set(f.priceLevel, {
                top: yPos.position,
                bottom: yPos.position + yPos.length,
                center: yPos.position + yPos.length / 2,
                height: yPos.length
            });
        }
        if (this._options.showImbalance !== false) {
            // Note: Consecutive imbalance highlighting is now handled by persistent rectangles
            // Only draw the individual imbalance indicators here
            this._drawImbalanceIndicators(ctx, fp, startX, width, priceToCoordinate, scope, cellGeometry, xPosStatic);
        }
        // Remove cumulative delta calculation and rendering
        if (this._options.showTable !== false && (d.volume || d.delta !== undefined)) {
            this._drawSummaryTable(ctx, d, startX, width, lowY, scope);
        }
    }

    _drawImbalanceIndicators(ctx, fp, startX, width, priceToCoordinate, scope, cellGeometry, xPosStatic) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        const imbalanceThreshold = this._options.imbalanceThreshold || 300;
        const lineLength = 6;
        if (fp.length < 2) return;

        const sortedFp = [...fp].sort((a, b) => a.priceLevel - b.priceLevel);

        ctx.strokeStyle = this._options.imbalanceColor || '#FFB433';
        ctx.lineWidth = 1; // finer line for subtlety
        ctx.globalAlpha = 0.9;

        // Precompute left/right x positions tight to footprint block
        const rightX = (xPosStatic.position + xPosStatic.length + 1);
        const leftX = (xPosStatic.position - 2);

        // Buy imbalance markers (right side)
        for (let i = 1; i < sortedFp.length; i++) {
            if (i - 1 === 0) continue; // skip very bottom baseline check per original logic
            const cur = sortedFp[i];
            const lower = sortedFp[i - 1];
            const curBuy = cur.buyVolume || 0;
            const lowerSell = lower.sellVolume || 0;
            if (this._detectImbalance(curBuy, lowerSell, imbalanceThreshold)) {
                const geom = cellGeometry.get(cur.priceLevel);
                if (geom) {
                    ctx.beginPath();
                    ctx.moveTo(rightX * h, geom.center * v - (lineLength / 2) * v);
                    ctx.lineTo(rightX * h, geom.center * v + (lineLength / 2) * v);
                    ctx.stroke();
                }
            }
        }

        // Sell imbalance markers (left side)
        for (let i = 0; i < sortedFp.length - 1; i++) {
            const cur = sortedFp[i];
            const higher = sortedFp[i + 1];
            const curSell = cur.sellVolume || 0;
            const higherBuy = higher.buyVolume || 0;
            if (this._detectImbalance(curSell, higherBuy, imbalanceThreshold)) {
                const geom = cellGeometry.get(cur.priceLevel);
                if (geom) {
                    ctx.beginPath();
                    ctx.moveTo(leftX * h, geom.center * v - (lineLength / 2) * v);
                    ctx.lineTo(leftX * h, geom.center * v + (lineLength / 2) * v);
                    ctx.stroke();
                }
            }
        }

        ctx.globalAlpha = 1;
    }

    _drawConsecutiveImbalanceHighlights(ctx, fp, startX, width, priceToCoordinate, scope, cellGeometry, xPosStatic) {
        const imbalanceThreshold = this._options.imbalanceThreshold || 300;
        if (fp.length < 2) return;

        const sortedFp = [...fp].sort((a, b) => a.priceLevel - b.priceLevel);

        // Identify all imbalance cells
        const imbalanceCells = new Set();

        // Check buy imbalances (right side)
        for (let i = 1; i < sortedFp.length; i++) {
            if (i - 1 === 0) continue; // skip very bottom baseline check per original logic
            const cur = sortedFp[i];
            const lower = sortedFp[i - 1];
            const curBuy = cur.buyVolume || 0;
            const lowerSell = lower.sellVolume || 0;
            if (this._detectImbalance(curBuy, lowerSell, imbalanceThreshold)) {
                imbalanceCells.add(cur.priceLevel);
            }
        }

        // Check sell imbalances (left side)
        for (let i = 0; i < sortedFp.length - 1; i++) {
            const cur = sortedFp[i];
            const higher = sortedFp[i + 1];
            const curSell = cur.sellVolume || 0;
            const higherBuy = higher.buyVolume || 0;
            if (this._detectImbalance(curSell, higherBuy, imbalanceThreshold)) {
                imbalanceCells.add(cur.priceLevel);
            }
        }

        // Find consecutive imbalance groups
        const consecutiveGroups = this._findConsecutiveImbalanceGroups(sortedFp, imbalanceCells);

        // Draw highlight rectangles for groups with 2+ consecutive cells
        this._drawImbalanceHighlightRectangles(ctx, consecutiveGroups, cellGeometry, xPosStatic, scope);
    }

    _findConsecutiveImbalanceGroups(sortedFp, imbalanceCells) {
        const groups = [];
        let currentGroup = [];

        for (let i = 0; i < sortedFp.length; i++) {
            const priceLevel = sortedFp[i].priceLevel;

            if (imbalanceCells.has(priceLevel)) {
                currentGroup.push(priceLevel);
            } else {
                // End of consecutive sequence
                if (currentGroup.length >= 2) {
                    groups.push([...currentGroup]);
                }
                currentGroup = [];
            }
        }

        // Check final group
        if (currentGroup.length >= 2) {
            groups.push(currentGroup);
        }

        return groups;
    }

    _drawImbalanceHighlightRectangles(ctx, consecutiveGroups, cellGeometry, xPosStatic, scope) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;

        if (consecutiveGroups.length === 0) return;

        ctx.save();
        ctx.globalAlpha = this._options.consecutiveImbalanceAlpha || 0.15;
        ctx.fillStyle = this._options.consecutiveImbalanceColor || '#FFB433';

        for (const group of consecutiveGroups) {
            if (group.length < 2) continue;

            // Find the geometry for the first and last cells in the group
            const firstGeom = cellGeometry.get(group[0]);
            const lastGeom = cellGeometry.get(group[group.length - 1]);

            if (!firstGeom || !lastGeom) continue;

            // Calculate rectangle dimensions
            const rectTop = Math.min(firstGeom.top, lastGeom.top);
            const rectBottom = Math.max(firstGeom.bottom, lastGeom.bottom);
            const rectHeight = rectBottom - rectTop;

            // Extend rectangle rightward beyond the footprint cells
            const rectLeft = xPosStatic.position;
            const rectWidth = xPosStatic.length + (this._options.consecutiveImbalanceExtension || 20);

            // Draw the highlight rectangle
            ctx.fillRect(
                rectLeft * h,
                rectTop * v,
                rectWidth * h,
                rectHeight * v
            );
        }

        ctx.restore();
    }

    _detectImbalance(largerVolume, smallerVolume, thresholdPercent) {
        if (smallerVolume === 0) return largerVolume > 0;
        return largerVolume >= (thresholdPercent / 100) * smallerVolume;
    }

    _updatePersistentRectangles(bars, priceToCoordinate) {
        if (!bars || bars.length === 0) return;

        // Remove rectangles that are crossed by any candle's close price
        this._persistentRectangles = this._persistentRectangles.filter(rect => {
            for (const bar of bars) {
                if (bar?.originalData && isValidOHLCData(bar.originalData)) {
                    const closePrice = bar.originalData.close;
                    // Check if close price is within the rectangle's price range
                    if (closePrice >= rect.minPrice && closePrice <= rect.maxPrice) {
                        return false; // Remove this rectangle
                    }
                }
            }
            return true; // Keep this rectangle
        });

        // Add new rectangles from the latest bar with footprint data
        const latestBar = bars[bars.length - 1];
        if (latestBar?.originalData?.footprint && Array.isArray(latestBar.originalData.footprint)) {
            const newRectangles = this._findNewPersistentRectangles(latestBar, bars.length - 1);
            this._persistentRectangles.push(...newRectangles);
        }
    }

    _findNewPersistentRectangles(bar, barIndex) {
        const fp = bar.originalData.footprint;
        const imbalanceThreshold = this._options.imbalanceThreshold || 300;
        const rectangles = [];

        if (fp.length < 2) return rectangles;

        const sortedFp = [...fp].sort((a, b) => a.priceLevel - b.priceLevel);

        // Identify all imbalance cells
        const imbalanceCells = new Set();

        // Check buy imbalances
        for (let i = 1; i < sortedFp.length; i++) {
            if (i - 1 === 0) continue;
            const cur = sortedFp[i];
            const lower = sortedFp[i - 1];
            const curBuy = cur.buyVolume || 0;
            const lowerSell = lower.sellVolume || 0;
            if (this._detectImbalance(curBuy, lowerSell, imbalanceThreshold)) {
                imbalanceCells.add(cur.priceLevel);
            }
        }

        // Check sell imbalances
        for (let i = 0; i < sortedFp.length - 1; i++) {
            const cur = sortedFp[i];
            const higher = sortedFp[i + 1];
            const curSell = cur.sellVolume || 0;
            const higherBuy = higher.buyVolume || 0;
            if (this._detectImbalance(curSell, higherBuy, imbalanceThreshold)) {
                imbalanceCells.add(cur.priceLevel);
            }
        }

        // Find consecutive groups and create persistent rectangles
        const consecutiveGroups = this._findConsecutiveImbalanceGroups(sortedFp, imbalanceCells);

        for (const group of consecutiveGroups) {
            if (group.length >= 2) {
                const minPrice = Math.min(...group);
                const maxPrice = Math.max(...group);
                rectangles.push({
                    minPrice,
                    maxPrice,
                    startBarIndex: barIndex,
                    startX: bar.x
                });
            }
        }

        return rectangles;
    }

    _drawPersistentRectangles(ctx, priceToCoordinate, scope, from, to) {
        if (this._persistentRectangles.length === 0) return;

        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        const d = this._data;

        ctx.save();
        ctx.globalAlpha = this._options.consecutiveImbalanceAlpha || 0.15;
        ctx.fillStyle = this._options.consecutiveImbalanceColor || '#FFB433';

        for (const rect of this._persistentRectangles) {
            // Calculate the rectangle coordinates
            const topY = priceToCoordinate(rect.maxPrice);
            const bottomY = priceToCoordinate(rect.minPrice);
            const rectHeight = Math.abs(bottomY - topY);

            // Find the leftmost visible bar position
            let leftX = rect.startX;
            if (from < d.bars.length && d.bars[from]) {
                leftX = d.bars[from].x;
            }

            // Find the rightmost visible bar position
            let rightX = leftX;
            if (to - 1 < d.bars.length && d.bars[to - 1]) {
                rightX = d.bars[to - 1].x + (d.barSpacing || 10);
            }

            // Extend rectangle to cover the visible range
            const rectWidth = rightX - leftX + (this._options.consecutiveImbalanceExtension || 20);

            // Draw the persistent rectangle
            ctx.fillRect(
                leftX * h,
                Math.min(topY, bottomY) * v,
                rectWidth * h,
                rectHeight * v
            );
        }

        ctx.restore();
    }

    // _calculateCumulativeDelta removed

    // _drawGrid removed

    _drawSummaryTable(ctx, d, startX, width, lowY, scope) {
        const { horizontalPixelRatio: h, verticalPixelRatio: v } = scope;
        const tableHeight = 34;
        const tableY = lowY + 10;
        ctx.globalAlpha = 0.9;
        ctx.fillStyle = this._options.tableBackgroundColor || '#1E222D';
        const xPos = positionsLine(startX + width / 2, h, width);
        const yPos = positionsBox(tableY, tableY + tableHeight, v);
        const radius = 3;
        ctx.beginPath();
        ctx.roundRect(xPos.position, yPos.position, xPos.length, yPos.length, radius);
        ctx.fill();
        ctx.globalAlpha = 1;
        ctx.strokeStyle = this._options.tableBorderColor || '#2A2E39';
        ctx.lineWidth = 1;
        ctx.stroke();
        ctx.font = this._font9;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        const centerX = xPos.position + xPos.length / 2;
        const lineHeight = (yPos.length - 10) / 2;
        const startY = yPos.position + 5 + lineHeight / 2;
        ctx.fillStyle = this._options.textColor || '#fff';
        ctx.fillText(`Vol: ${this._formatNum(d.volume || 0)}`, centerX, startY);
        const delta = d.delta || 0;
        ctx.fillStyle = delta >= 0 ? this._options.upColor : this._options.downColor;
        ctx.fillText(`Δ: ${delta >= 0 ? '+' : ''}${this._formatNum(delta)}`, centerX, startY + lineHeight);
        ctx.globalAlpha = 1;
    }

    _formatNum(n) {
        if (n < 1000) return n.toString();
        if (n >= 1e6) return (n / 1e6).toFixed(1) + 'M';
        return (n / 1e3).toFixed(1) + 'K';
    }

    _formatText(buy, sell, delta) {
        switch (this._options.volumeDisplayMode) {
            case 'split': return `${this._formatNum(sell)}x${this._formatNum(buy)}`;
            case 'delta': return delta >= 0 ? `+${this._formatNum(delta)}` : `${this._formatNum(delta)}`;
            case 'total': return this._formatNum(buy + sell);
            default: return `${this._formatNum(sell)}x${this._formatNum(buy)}`;
        }
    }
}

/**
 * Optimized footprint series with proper OHLC support for MagnetOHLC
 * Fixed to create separate renderer instances per chart to prevent blinking in multi-chart layouts
 */
export const FootprintSeries = {
    // Create a new instance for each chart instead of sharing
    create() {
        return Object.create(this, {
            _renderer: { value: null, writable: true },
            _options: { value: null, writable: true },
            _defaultOptions: { value: null, writable: true }
        });
    },
    
    renderer() {
        if (!this._renderer) {
            this._renderer = new FootprintRenderer();
        }
        return this._renderer;
    },
    
    update(data, options) {
        if (!this._renderer) {
            this._renderer = new FootprintRenderer();
        }
        this._options = this._options ? Object.assign(this._options, options) : Object.assign({}, this.defaultOptions(), options);
        this._renderer.update(data, this._options);
    },
    
    /**
     * Price value builder for MagnetOHLC - returns [open, high, low, close]
     * This is critical for proper crosshair magnetization to OHLC values
     */
    priceValueBuilder(row) {
        if (!isValidOHLCData(row)) {
            return [0, 0, 0, 0];
        }
        return [row.open, row.high, row.low, row.close];
    },
    
    isWhitespace(row) {
        return !isValidOHLCData(row);
    },
    
    defaultOptions() {
        if (!this._defaultOptions) {
            this._defaultOptions = {
                upColor: '#089981', 
                downColor: '#F23645', 
                borderUpColor: '#089981', 
                borderDownColor: '#F23645',
                wickUpColor: '#089981', 
                wickDownColor: '#F23645', 
                borderVisible: true, 
                wickVisible: true,
                neutralColor: '#B2B5BE', 
                cellBorderColor: '#333', 
                textColor: '#fff',
                volumeDisplayMode: 'split', 
                visible: true, 
                showTable: true, 
                tableBackgroundColor: '#1E222D', 
                tableBorderColor: '#2A2E39',
                showImbalance: true,
                imbalanceThreshold: 300,
                imbalanceColor: '#FFFF00',
                consecutiveImbalanceColor: '#FFB433',
                consecutiveImbalanceAlpha: 0.15,
                consecutiveImbalanceExtension: 20
            };
        }
        return this._defaultOptions;
    },
    
    applyOptions(options) {
        this._options = Object.assign({}, this._options || this.defaultOptions(), options);
        return this;
    },
    
    destroy() {
        if (this._renderer) {
            if (this._renderer._textCache) {
                this._renderer._textCache.clear();
            }
            if (this._renderer._persistentRectangles) {
                this._renderer._persistentRectangles = [];
            }
        }
        this._renderer = null;
        this._options = null;
        this._defaultOptions = null;
    }
};
