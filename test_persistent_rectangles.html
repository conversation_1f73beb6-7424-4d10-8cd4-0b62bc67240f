<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Persistent Imbalance Rectangles Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1e1e1e;
            color: white;
        }
        #chart-container {
            width: 100%;
            height: 600px;
            background-color: #2a2a2a;
            border: 1px solid #444;
        }
        .controls {
            margin-bottom: 20px;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .info {
            margin-bottom: 20px;
            color: #ccc;
            background-color: #333;
            padding: 15px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Persistent Imbalance Rectangles Test</h1>
    
    <div class="info">
        <h3>How it works:</h3>
        <ul>
            <li><strong>Persistent Rectangles:</strong> When consecutive imbalances are detected, rectangles are drawn that persist across future candles</li>
            <li><strong>Auto-Deletion:</strong> If any candle's close price crosses through a rectangle, it gets automatically deleted</li>
            <li><strong>Visual Feedback:</strong> Rectangles extend from their origin to the current candle, providing ongoing support/resistance levels</li>
        </ul>
    </div>
    
    <div class="controls">
        <button onclick="loadTestData()">Load Test Data</button>
        <button onclick="addCrossingCandle()">Add Crossing Candle</button>
        <button onclick="addNormalCandle()">Add Normal Candle</button>
        <button onclick="clearData()">Clear All Data</button>
    </div>
    
    <div id="chart-container"></div>

    <script type="module">
        import { createChart } from './static/js/lightweight-charts.standalone.development.js';
        import { FootprintSeries } from './static/js/plugins/footprint.js';

        let chart;
        let footprintSeries;
        let testData = [];

        function initChart() {
            chart = createChart(document.getElementById('chart-container'), {
                width: document.getElementById('chart-container').clientWidth,
                height: 600,
                layout: {
                    background: { color: '#1e1e1e' },
                    textColor: '#ffffff',
                },
                grid: {
                    vertLines: { color: '#2B2B43' },
                    horzLines: { color: '#2B2B43' },
                },
                crosshair: {
                    mode: 1,
                },
                rightPriceScale: {
                    borderColor: '#485c7b',
                },
                timeScale: {
                    borderColor: '#485c7b',
                },
            });

            footprintSeries = chart.addCustomSeries(FootprintSeries.create(), {
                upColor: '#089981',
                downColor: '#F23645',
                showImbalance: true,
                imbalanceThreshold: 300,
                consecutiveImbalanceColor: '#FFB433',
                consecutiveImbalanceAlpha: 0.25,
                consecutiveImbalanceExtension: 30,
                visible: true
            });
        }

        function generateImbalanceCandle(time, basePrice, hasImbalance = true) {
            const open = basePrice + (Math.random() - 0.5) * 0.5;
            const close = open + (Math.random() - 0.5) * 1;
            const high = Math.max(open, close) + Math.random() * 0.5;
            const low = Math.min(open, close) - Math.random() * 0.5;
            
            const footprint = [];
            const numLevels = 10;
            const priceStep = (high - low) / numLevels;
            
            for (let j = 0; j < numLevels; j++) {
                const priceLevel = low + j * priceStep;
                let buyVolume = Math.floor(Math.random() * 300) + 100;
                let sellVolume = Math.floor(Math.random() * 300) + 100;
                
                if (hasImbalance && j >= 4 && j <= 7) {
                    // Create strong consecutive imbalances
                    buyVolume = Math.floor(Math.random() * 200) + 1000;
                    sellVolume = Math.floor(Math.random() * 100) + 50;
                }
                
                footprint.push({
                    priceLevel: priceLevel,
                    buyVolume: buyVolume,
                    sellVolume: sellVolume
                });
            }
            
            const totalVolume = footprint.reduce((sum, f) => sum + f.buyVolume + f.sellVolume, 0);
            const delta = footprint.reduce((sum, f) => sum + f.buyVolume - f.sellVolume, 0);
            
            return {
                time: time,
                open: open,
                high: high,
                low: low,
                close: close,
                volume: totalVolume,
                delta: delta,
                footprint: footprint
            };
        }

        function generateCrossingCandle(time, basePrice, crossingPrice) {
            const open = basePrice + (Math.random() - 0.5) * 0.5;
            const close = crossingPrice; // Force close to cross the rectangle
            const high = Math.max(open, close, crossingPrice) + Math.random() * 0.2;
            const low = Math.min(open, close, crossingPrice) - Math.random() * 0.2;
            
            const footprint = [];
            const numLevels = 8;
            const priceStep = (high - low) / numLevels;
            
            for (let j = 0; j < numLevels; j++) {
                const priceLevel = low + j * priceStep;
                const buyVolume = Math.floor(Math.random() * 200) + 100;
                const sellVolume = Math.floor(Math.random() * 200) + 100;
                
                footprint.push({
                    priceLevel: priceLevel,
                    buyVolume: buyVolume,
                    sellVolume: sellVolume
                });
            }
            
            const totalVolume = footprint.reduce((sum, f) => sum + f.buyVolume + f.sellVolume, 0);
            const delta = footprint.reduce((sum, f) => sum + f.buyVolume - f.sellVolume, 0);
            
            return {
                time: time,
                open: open,
                high: high,
                low: low,
                close: close,
                volume: totalVolume,
                delta: delta,
                footprint: footprint
            };
        }

        window.loadTestData = function() {
            testData = [];
            const baseTime = Math.floor(Date.now() / 1000) - 3600; // 1 hour ago
            let basePrice = 100;
            
            // Create initial candles with imbalances
            for (let i = 0; i < 5; i++) {
                const time = baseTime + i * 300; // 5-minute intervals
                const hasImbalance = i === 2; // Create imbalance in the 3rd candle
                testData.push(generateImbalanceCandle(time, basePrice, hasImbalance));
                basePrice += (Math.random() - 0.5) * 0.5;
            }
            
            footprintSeries.setData(testData);
            console.log('Test data loaded with imbalance rectangles');
        };

        window.addCrossingCandle = function() {
            if (testData.length === 0) {
                alert('Please load test data first');
                return;
            }
            
            const lastCandle = testData[testData.length - 1];
            const newTime = lastCandle.time + 300;
            
            // Create a candle that crosses through the imbalance rectangle
            // Assuming the imbalance is around price levels 100.5-101.5
            const crossingPrice = 100.8; // This should cross the rectangle
            const newCandle = generateCrossingCandle(newTime, lastCandle.close, crossingPrice);
            
            testData.push(newCandle);
            footprintSeries.setData([...testData]);
            console.log('Added crossing candle - rectangles should be deleted');
        };

        window.addNormalCandle = function() {
            if (testData.length === 0) {
                alert('Please load test data first');
                return;
            }
            
            const lastCandle = testData[testData.length - 1];
            const newTime = lastCandle.time + 300;
            const newCandle = generateImbalanceCandle(newTime, lastCandle.close, false);
            
            testData.push(newCandle);
            footprintSeries.setData([...testData]);
            console.log('Added normal candle - rectangles should persist');
        };

        window.clearData = function() {
            testData = [];
            footprintSeries.setData([]);
            console.log('Data cleared');
        };

        // Initialize chart on page load
        initChart();
    </script>
</body>
</html>
