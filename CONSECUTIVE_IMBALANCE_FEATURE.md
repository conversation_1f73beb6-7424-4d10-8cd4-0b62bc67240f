# Consecutive Imbalance Highlighting Feature

## Overview

This feature enhances the footprint chart by adding visual highlighting for consecutive imbalance cells within individual candles. When two or more contiguous imbalance cells are detected within the same candle, a background rectangle is drawn to highlight this clustered imbalance region.

## Visual Enhancement

The highlighting consists of:
- **Background Rectangle**: Drawn behind consecutive imbalance cells
- **Horizontal Span**: Extends from the left edge of the footprint cells rightward beyond the last cell
- **Vertical Span**: Covers all consecutive imbalance cells in the sequence
- **Styling**: Uses a distinct background color with configurable transparency

## Configuration Options

The feature adds three new configuration options to the FootprintSeries:

### `consecutiveImbalanceColor`
- **Type**: String (CSS color)
- **Default**: `'#FFB433'` (orange/yellow)
- **Description**: Background color for the highlight rectangle

### `consecutiveImbalanceAlpha`
- **Type**: Number (0.0 - 1.0)
- **Default**: `0.15`
- **Description**: Transparency level of the highlight rectangle

### `consecutiveImbalanceExtension`
- **Type**: Number (pixels)
- **Default**: `20`
- **Description**: How far the rectangle extends rightward beyond the footprint cells

## Usage Example

```javascript
const footprintSeries = chart.addCustomSeries(FootprintSeries.create(), {
    // Standard footprint options
    upColor: '#089981',
    downColor: '#F23645',
    showImbalance: true,
    imbalanceThreshold: 300,
    
    // New consecutive imbalance highlighting options
    consecutiveImbalanceColor: '#FFB433',
    consecutiveImbalanceAlpha: 0.15,
    consecutiveImbalanceExtension: 20
});
```

## How It Works

1. **Imbalance Detection**: Uses the existing imbalance detection logic to identify individual imbalance cells
2. **Consecutive Grouping**: Analyzes the sorted price levels to find groups of 2+ adjacent imbalance cells
3. **Rectangle Drawing**: For each qualifying group, draws a background rectangle that:
   - Starts at the left edge of the footprint cells
   - Extends rightward by the configured extension amount
   - Spans vertically from the top of the first cell to the bottom of the last cell in the group
4. **Layering**: The highlight rectangles are drawn before the individual imbalance indicators, ensuring proper visual layering

## Benefits for Traders

- **Quick Identification**: Easily spot areas of clustered order flow imbalance
- **Pattern Recognition**: Identify nested imbalance regions that may indicate strong support/resistance
- **Visual Clarity**: Distinguish between isolated imbalances and significant imbalance clusters
- **Enhanced Analysis**: Better understand the depth and concentration of order flow imbalances

## Implementation Details

The feature is implemented in the `footprint.js` file with the following key methods:

- `_drawConsecutiveImbalanceHighlights()`: Main method that orchestrates the highlighting
- `_findConsecutiveImbalanceGroups()`: Identifies groups of consecutive imbalance cells
- `_drawImbalanceHighlightRectangles()`: Renders the background rectangles

The implementation integrates seamlessly with the existing imbalance detection logic and respects the `showImbalance` configuration option.

## Testing

A comprehensive test file (`test_consecutive_imbalance.html`) is provided that demonstrates:
- Loading test data with various consecutive imbalance patterns
- Interactive controls for adjusting highlight appearance
- Real-time configuration changes
- Different imbalance clustering scenarios

## Compatibility

This feature is fully backward compatible and does not affect existing footprint chart functionality. Charts without consecutive imbalances will display normally, and the feature only activates when 2+ adjacent imbalance cells are detected.
