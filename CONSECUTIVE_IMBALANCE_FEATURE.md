# Persistent Consecutive Imbalance Rectangles Feature

## Overview

This feature enhances the footprint chart by adding persistent visual highlighting for consecutive imbalance cells. When two or more contiguous imbalance cells are detected within a candle, a background rectangle is created that persists across future candles until a candle's close price crosses through it, at which point the rectangle is automatically deleted.

## Visual Enhancement

The persistent highlighting consists of:
- **Background Rectangle**: Drawn behind consecutive imbalance cells and extends across future candles
- **Horizontal Span**: Extends from the origin candle to the current candle (rightward)
- **Vertical Span**: Covers the price range of all consecutive imbalance cells in the sequence
- **Persistence**: Rectangles remain visible until crossed by a candle's close price
- **Auto-Deletion**: Rectangles are automatically removed when any candle's close price intersects the rectangle's price range
- **Styling**: Uses a distinct background color with configurable transparency

## Configuration Options

The feature adds three new configuration options to the FootprintSeries:

### `consecutiveImbalanceColor`
- **Type**: String (CSS color)
- **Default**: `'#FFB433'` (orange/yellow)
- **Description**: Background color for the highlight rectangle

### `consecutiveImbalanceAlpha`
- **Type**: Number (0.0 - 1.0)
- **Default**: `0.15`
- **Description**: Transparency level of the highlight rectangle

### `consecutiveImbalanceExtension`
- **Type**: Number (pixels)
- **Default**: `20`
- **Description**: How far the rectangle extends rightward beyond the footprint cells

## Usage Example

```javascript
const footprintSeries = chart.addCustomSeries(FootprintSeries.create(), {
    // Standard footprint options
    upColor: '#089981',
    downColor: '#F23645',
    showImbalance: true,
    imbalanceThreshold: 300,
    
    // New consecutive imbalance highlighting options
    consecutiveImbalanceColor: '#FFB433',
    consecutiveImbalanceAlpha: 0.15,
    consecutiveImbalanceExtension: 20
});
```

## How It Works

1. **Imbalance Detection**: Uses the existing imbalance detection logic to identify individual imbalance cells
2. **Consecutive Grouping**: Analyzes the sorted price levels to find groups of 2+ adjacent imbalance cells
3. **Rectangle Creation**: For each qualifying group, creates a persistent rectangle with:
   - Price range covering the minimum to maximum price of the consecutive imbalance cells
   - Starting position at the candle where the imbalance was detected
   - Unique identifier for tracking across candles
4. **Persistence Management**: On each new candle:
   - Checks if any existing rectangles are crossed by the candle's close price
   - Removes crossed rectangles automatically
   - Adds new rectangles for any new consecutive imbalances detected
5. **Visual Rendering**: Persistent rectangles are drawn:
   - Behind all other chart elements for proper layering
   - Extending from their origin candle to the current visible candle
   - With configurable color, transparency, and extension width

## Benefits for Traders

- **Persistent Support/Resistance Levels**: Rectangles act as ongoing visual markers for significant imbalance zones
- **Automatic Level Invalidation**: When price crosses through a rectangle, it's automatically removed, indicating the level has been breached
- **Historical Context**: See how past imbalance zones have held or been broken over time
- **Quick Identification**: Easily spot areas of clustered order flow imbalance that remain relevant
- **Pattern Recognition**: Identify nested imbalance regions that may indicate strong support/resistance
- **Visual Clarity**: Distinguish between isolated imbalances and significant persistent imbalance clusters
- **Enhanced Analysis**: Better understand the depth and concentration of order flow imbalances with temporal persistence

## Implementation Details

The feature is implemented in the `footprint.js` file with the following key methods:

### Core Methods
- `_updatePersistentRectangles()`: Manages the lifecycle of persistent rectangles, removing crossed ones and adding new ones
- `_findNewPersistentRectangles()`: Identifies new consecutive imbalance groups and creates rectangle objects
- `_drawPersistentRectangles()`: Renders all active persistent rectangles across the visible chart range
- `_findConsecutiveImbalanceGroups()`: Identifies groups of consecutive imbalance cells (shared with original implementation)

### Data Structures
- `_persistentRectangles`: Array storing active rectangle objects with price ranges and origin information
- Each rectangle contains: `minPrice`, `maxPrice`, `startBarIndex`, `startX`

### Integration Points
- Integrated into the main `_drawImpl()` method for automatic processing
- Respects the `showImbalance` configuration option
- Automatic cleanup in the `destroy()` method to prevent memory leaks
- Smart data refresh detection to clear rectangles when appropriate

## Testing

A comprehensive test file (`test_persistent_rectangles.html`) is provided that demonstrates:
- Loading test data with consecutive imbalance patterns that create persistent rectangles
- Adding normal candles that don't cross rectangles (rectangles persist)
- Adding crossing candles that intersect rectangle price ranges (rectangles are deleted)
- Interactive controls for testing the persistence and deletion logic
- Visual feedback showing how rectangles behave over time

## Compatibility

This feature is fully backward compatible and does not affect existing footprint chart functionality. Charts without consecutive imbalances will display normally, and the feature only activates when 2+ adjacent imbalance cells are detected. The persistent rectangle system operates independently of other chart features and automatically manages its own memory and lifecycle.

## Performance Considerations

- **Memory Efficient**: Rectangles are automatically cleaned up when crossed or when data is refreshed
- **Optimized Rendering**: Persistent rectangles are drawn in a single pass before other chart elements
- **Smart Updates**: Only processes new data for rectangle creation, existing rectangles are managed separately
- **Configurable**: All visual aspects can be adjusted to balance visibility with performance
