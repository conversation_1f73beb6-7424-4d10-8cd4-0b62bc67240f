// Navbar and style utilities for trading platform UI

import { showSettingsPopup, injectSettingsStyles } from './settings.js';

// Store navbar visibility state
let isNavbarVisible = true;

// Setup navbar toggle keyboard shortcuts
function setupNavbarToggle() {
    // Add keyboard shortcut for toggling navbar (Ctrl+Space)
    document.addEventListener('keydown', (e) => {
        if (e.ctrlKey && e.code === 'Space') {
            e.preventDefault();
            toggleNavbarVisibility();
        }
    });
}

export function createNavbar() {
    const navbar = document.createElement('nav');
    navbar.id = 'main-navbar';
    
    // Apply inline styles for critical rendering path
    const navStyles = {
        position: 'fixed', 
        top: '0', 
        left: '0', 
        width: '100%', 
        height: '35px',
        background: '#131722', 
        color: '#D1D4DC', 
        display: 'flex', 
        alignItems: 'center',
        justifyContent: 'space-between', 
        zIndex: '1000', 
        boxShadow: '0 1px 0 #23263A',
        userSelect: 'none', 
        padding: '0',
        fontSize: '12px'
    };
    
    Object.assign(navbar.style, navStyles);
    
    // Create HTML structure for the navbar
    navbar.innerHTML = `
        <div id="navbar-left" class="navbar-section">
            <div id="symbol-info" class="symbol-container">
                <span class="search-icon">&#128269;</span>
                <span id="current-symbol" class="current-symbol"></span>
            </div>
            <div id="timeframe-selector" class="timeframe-container"></div>
        </div>
        <div id="navbar-right" class="navbar-section">
            <div class="layout-controls">
                <button id="layout-1" class="layout-btn active" title="Single Chart">1</button>
                <button id="layout-2" class="layout-btn" title="Two Charts Side by Side">2</button>
                <button id="layout-3" class="layout-btn" title="Three Charts Row">3</button>
                <button id="layout-4" class="layout-btn" title="Four Charts Grid">4</button>
            </div>
            <div class="settings-controls">
                <button id="chart-type-switch" class="layout-btn" title="Switch Chart Type" style="margin-right:8px;">🦶</button>
                <button id="settings-btn" class="settings-btn" title="Settings">
                    <span class="settings-icon">⚙️</span>
                </button>
            </div>
        </div>
    `;
    
    document.body.prepend(navbar);
    
    // Layout button event handling will be set up in chart.js
    // to ensure proper state management and persistence

    // Setup settings button
    const settingsBtn = navbar.querySelector('#settings-btn');
    if (settingsBtn) {
        settingsBtn.addEventListener('click', showSettingsPopup);
    }

    // Chart type switcher button: normal click toggles chart type, shift+click opens settings popup
    const chartTypeBtn = navbar.querySelector('#chart-type-switch');
    if (chartTypeBtn) {
        chartTypeBtn.addEventListener('click', (e) => {
            if (e.shiftKey) {
                // Show footprint settings popup
                let popup = document.getElementById('chart-settings-popup');
                if (popup) return; // Already open
                popup = document.createElement('div');
                popup.id = 'chart-settings-popup';
                popup.style.position = 'absolute';
                // Position near the icon, but keep inside viewport
                const rect = chartTypeBtn.getBoundingClientRect();
                const popupWidth = 220; // matches style
                const popupHeight = 120; // estimated height
                let left = rect.left;
                let top = rect.bottom + 6;
                // If popup would overflow right, align its right edge with icon's right edge
                if (left + popupWidth > window.innerWidth - 8) {
                    left = rect.right - popupWidth;
                }
                if (left < 8) left = 8;
                // Clamp top so popup doesn't overflow bottom edge
                if (top + popupHeight > window.innerHeight - 8) {
                    top = window.innerHeight - popupHeight - 8;
                }
                if (top < 8) top = 8;
                popup.style.top = `${top}px`;
                popup.style.left = `${left}px`;
                popup.style.background = '#23263A';
                popup.style.color = '#B2B5BE';
                popup.style.border = '1px solid #23263A';
                popup.style.borderRadius = '8px';
                popup.style.padding = '10px 10px 8px 10px';
                popup.style.zIndex = '2000';
                popup.style.boxShadow = '0 2px 16px rgba(0,0,0,0.18)';
                popup.style.width = '220px';
                popup.style.minWidth = '0';
                popup.style.maxWidth = '90vw';
                popup.innerHTML = `
                    <div style="font-size:13px;font-weight:600;margin-bottom:8px;text-align:center;">Footprint Settings</div>
                    <div style="margin-bottom:8px;display:flex;flex-direction:column;gap:6px;">
                        <div style="display:flex;align-items:center;justify-content:space-between;">
                            <label for="popup-bucket-size-input" style="font-size:11px;color:#B2B5BE;">Footprint Bucket:</label>
                            <input id="popup-bucket-size-input" type="number" step="0.01" min="0.01" value="0.05" style="width:60px;font-size:11px;padding:1px 4px;border-radius:2px;border:1px solid #363C4E;background:#181A20;color:#D1D4DC;text-align:right;">
                        </div>
                        <div style="display:flex;align-items:center;justify-content:space-between;">
                            <label for="popup-multiplier-input" style="font-size:11px;color:#B2B5BE;">Footprint Multiplier:</label>
                            <input id="popup-multiplier-input" type="number" step="1" min="1" value="100" style="width:60px;font-size:11px;padding:1px 4px;border-radius:2px;border:1px solid #363C4E;background:#181A20;color:#D1D4DC;text-align:right;">
                        </div>
                    </div>
                    <div style="display:flex;justify-content:flex-end;gap:6px;">
                        <button id="close-chart-settings-btn" style="background:#363C4E;color:#fff;border:none;border-radius:2px;padding:4px 10px;font-size:11px;cursor:pointer;">Close</button>
                        <button id="apply-chart-settings-btn" style="background:#2962FF;color:#fff;border:none;border-radius:2px;padding:4px 10px;font-size:11px;cursor:pointer;">Apply</button>
                    </div>
                `;
                document.body.appendChild(popup);
                // Set current values from chart (if available)
                const bucketInput = popup.querySelector('#popup-bucket-size-input');
                const multiplierInput = popup.querySelector('#popup-multiplier-input');
                // Prefill with current chart settings from chart.js
                try {
                    // Ensure we get the correct selected chart index
                    let selectedIdx = window.selectedChartIndex || 0;
                    
                    // Get the selected chart ID using the correct index
                    const chartId = 'chart-' + selectedIdx;
                    
                    // Define function to update inputs with chart settings
                    function updateFootprintInputs(chartId) {
                        const saved = localStorage.getItem('chartSettings_' + chartId);
                        if (saved) {
                            const obj = JSON.parse(saved);
                            if (typeof obj.bucket_size === 'number') bucketInput.value = obj.bucket_size;
                            if (typeof obj.multiplier === 'number') multiplierInput.value = obj.multiplier;
                        } else {
                            // If no saved settings, get them from chart registry if available
                            const idx = parseInt(chartId.split('-')[1]);
                            if (window.chartRegistry && window.chartRegistry[idx]) {
                                const chartObj = window.chartRegistry[idx];
                                if (typeof chartObj.bucket_size === 'number') bucketInput.value = chartObj.bucket_size;
                                if (typeof chartObj.multiplier === 'number') multiplierInput.value = chartObj.multiplier;
                            }
                        }
                    }
                    
                    // Initial update with current chart settings
                    updateFootprintInputs(chartId);
                    
                    // Listen for chart selection changes while popup is open
                    function chartSelectionListener(e) {
                        // Update inputs when selected chart changes
                        const newChartId = 'chart-' + (window.selectedChartIndex || 0);
                        updateFootprintInputs(newChartId);
                    }
                    
                    // Add event listener for chart selection changes
                    document.addEventListener('chartSelected', chartSelectionListener);
                    
                    // Store the listener so we can remove it when popup closes
                    popup._chartSelectionListener = chartSelectionListener;
                    
                } catch (err) {
                    console.warn('Error loading chart settings for footprint popup:', err);
                }
                // Apply button
                popup.querySelector('#apply-chart-settings-btn').onclick = () => {
                    const bucket_size = parseFloat(bucketInput.value);
                    const multiplier = parseInt(multiplierInput.value);
                    document.dispatchEvent(new CustomEvent('chartSettingsChanged', {
                        detail: { bucket_size, multiplier }
                    }));
                    popup.remove();
                    document.removeEventListener('keydown', escListener);
                    // Remove chart selection listener if it exists
                    if (popup._chartSelectionListener) {
                        document.removeEventListener('chartSelected', popup._chartSelectionListener);
                    }
                };
                // Close button
                popup.querySelector('#close-chart-settings-btn').onclick = () => {
                    popup.remove();
                    document.removeEventListener('keydown', escListener);
                    // Remove chart selection listener if it exists
                    if (popup._chartSelectionListener) {
                        document.removeEventListener('chartSelected', popup._chartSelectionListener);
                    }
                };
                // Enter key applies settings and closes popup
                function enterListener(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        const bucket_size = parseFloat(bucketInput.value);
                        const multiplier = parseInt(multiplierInput.value);
                        document.dispatchEvent(new CustomEvent('chartSettingsChanged', {
                            detail: { bucket_size, multiplier }
                        }));
                        popup.remove();
                        document.removeEventListener('keydown', escListener);
                        document.removeEventListener('keydown', enterListener);
                        // Remove chart selection listener if it exists
                        if (popup._chartSelectionListener) {
                            document.removeEventListener('chartSelected', popup._chartSelectionListener);
                        }
                    }
                }
                document.addEventListener('keydown', enterListener);
                // ESC key closes popup
                function escListener(e) {
                    if (e.key === 'Escape') {
                        if (popup && document.body.contains(popup)) {
                            popup.remove();
                            document.removeEventListener('keydown', escListener);
                            document.removeEventListener('keydown', enterListener);
                            // Remove chart selection listener if it exists
                            if (popup._chartSelectionListener) {
                                document.removeEventListener('chartSelected', popup._chartSelectionListener);
                            }
                        }
                    }
                }
                document.addEventListener('keydown', escListener);
            } else {
                // Normal click: toggle chart type
                document.dispatchEvent(new CustomEvent('chartTypeSwitch', {}));
            }
        });
    }

    // Setup navbar toggle functionality
    setupNavbarToggle();
}


// Function to robustly toggle navbar visibility and always restore layout correctly

export function toggleNavbarVisibility() {
    const navbar = document.getElementById('main-navbar');
    const chartsGrid = document.querySelector('.charts-grid');
    if (!navbar) return;

    isNavbarVisible = !isNavbarVisible;

    // Set navbar style
    navbar.style.transition = 'transform 0.3s cubic-bezier(.4,0,.2,1)';
    navbar.style.transform = isNavbarVisible ? 'translateY(0)' : 'translateY(-40px)';
    navbar.style.zIndex = '1000';

    // Set grid style
    if (chartsGrid) {
        chartsGrid.style.transition = 'top 0.3s cubic-bezier(.4,0,.2,1), height 0.3s cubic-bezier(.4,0,.2,1)';
        chartsGrid.style.top = isNavbarVisible ? '40px' : '0';
        chartsGrid.style.height = isNavbarVisible ? 'calc(100vh - 40px)' : '100vh';
    }

    // Remove any existing reveal buttons (cleanup)
    const revealBtn = document.getElementById('navbar-reveal-btn');
    if (revealBtn) revealBtn.remove();

    // Only one event, after transition, for chart resize
    setTimeout(() => {
        document.dispatchEvent(new CustomEvent('navbarToggled', { detail: { visible: isNavbarVisible } }));
        requestAnimationFrame(() => window.dispatchEvent(new Event('resize')));
    }, 320);
}

export function injectStyles() {
    const style = document.createElement('style');
    style.innerHTML = `
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');
        
        :root {
            --bg-primary: #131722;
            --bg-secondary: #1E222D;
            --bg-element: #2A2E39;
            --bg-hover: #363C4E;
            --text-primary: #D1D4DC;
            --text-secondary: #787B86;
            --accent-blue: #2962FF;
            --accent-green: #22ab94;
            --accent-red: #f7525f;
            --border-color: #23263A;
        }
        
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            font-size: 13px;
        }
        
        .charts-grid {
            position: absolute;
            top: 40px;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100vw;
            height: calc(100vh - 40px);
            display: grid;
            background: var(--bg-primary);
            gap: 1px;
            transition: top 0.3s ease, height 0.3s ease;
        }
        
        .chart-container {
            width: 100%;
            height: 100%;
            background: var(--bg-secondary);
            overflow: hidden;
        }
        
        nav {
            user-select: none;
            background: var(--bg-secondary) !important;
            color: var(--text-primary) !important;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            border-bottom: 1px solid var(--border-color);
            height: 40px !important;
            min-height: 40px !important;
            max-height: 40px !important;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 13px;
            transition: transform 0.3s ease;
            transform: translateY(0);
        }
        
        .navbar-section {
            display: flex;
            align-items: center;
            height: 100%;
        }
        
        #navbar-left {
            padding-left: 8px;
            gap: 16px;
            flex: 1 1 auto;
        }
        
        #navbar-right {
            padding-right: 8px;
            gap: 8px;
            flex-shrink: 0;
        }
        
        .symbol-container {
            display: flex;
            align-items: center;
            gap: 6px;
            height: 100%;
            padding: 0 12px;
            cursor: pointer;
            border-right: 1px solid var(--border-color);
        }
        
        .symbol-container:hover {
            background: var(--bg-hover);
        }
        
        .search-icon {
            font-size: 14px;
            color: var(--text-secondary);
        }
        
        .current-symbol {
            font-weight: 600;
            font-size: 14px;
        }
        
        .timeframe-container {
            display: flex;
            align-items: center;
            height: 100%;
            gap: 2px;
            padding: 0 8px;
        }
        
        .layout-controls {
            display: flex;
            align-items: center;
            height: 100%;
            gap: 2px;
            padding: 0 4px 0 12px;
            border-left: 1px solid var(--border-color);
        }
        
        .layout-controls:before {
            content: "Layout";
            color: var(--text-secondary);
            margin-right: 6px;
        }
        
        .settings-controls {
            display: flex;
            align-items: center;
            height: 100%;
            padding: 0 8px 0 8px;
        }
        
        /* Common button styles */
        .layout-btn,
        .tf-btn,
        .navbar-toggle-btn,
        .settings-btn {
            background: var(--bg-element);
            color: var(--text-primary);
            border: none;
            border-radius: 2px;
            cursor: pointer;
            transition: background 0.15s;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .layout-btn:hover,
        .tf-btn:hover,
        .navbar-toggle-btn:hover,
        .settings-btn:hover {
            background: var(--bg-hover);
        }
        
        .layout-btn.active,
        .tf-btn.active {
            background: var(--accent-blue);
        }
        
        /* Specific styles */
        .layout-btn,
        .settings-btn {
            width: 24px;
            height: 24px;
        }
        
        .settings-icon {
            font-size: 14px;
        }
        
        .tf-btn {
            padding: 2px 8px;
        }
        
        /* Navbar toggle button styles */
        .navbar-toggle-container {
            display: flex;
            align-items: center;
            height: 100%;
            padding: 0 8px 0 12px;
            border-left: 1px solid var(--border-color);
        }
        
        .navbar-toggle-btn {
            width: 28px;
            height: 28px;
            transition: all 0.15s ease;
        }
        
        .navbar-toggle-btn:hover {
            transform: translateY(1px);
        }
        
        .toggle-icon {
            display: inline-block;
            transition: transform 0.2s ease;
        }
        
        .navbar-toggle-btn:hover .toggle-icon {
            transform: translateY(1px);
        }
        
        /* CSS for navbar reveal button removed */
	`;
	document.head.appendChild(style);
	
	// Also inject settings styles
	injectSettingsStyles();
}
