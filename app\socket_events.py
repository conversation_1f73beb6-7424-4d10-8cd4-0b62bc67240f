import threading
import time
import os
import sys
from flask_socketio import emit, disconnect, join_room, leave_room
from flask import request

# Add paths for imports
for path in ['core/fyers']:
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', path))

from core.fyers.fyers_data import FyersDataFeed

# Global state for managing subscriptions and live data feeds
live_feeds = {}  # symbol -> {feed_instance, subscribers}
subscriber_rooms = {}  # room_id -> {symbol, timeframe, bucket_size, multiplier}
feed_threads = {}  # symbol -> thread

def register_socket_events(socketio):
    """Register all socket event handlers"""
    
    @socketio.on('connect')
    def handle_connect():
        print(f"Client connected: {request.sid}")
        emit('connected', {'data': 'Connected to TradeLab'})
    
    @socketio.on('disconnect')
    def handle_disconnect():
        print(f"Client disconnected: {request.sid}")
        # Clean up subscriptions for this client
        cleanup_client_subscriptions(request.sid)
    
    @socketio.on('subscribe_symbol')
    def handle_subscribe_symbol(data):
        """
        Subscribe to live updates for a symbol with specific parameters
        Expected data: {
            'symbol': 'NSE:NIFTY25AUGFUT',
            'timeframe': '5m',
            'bucket_size': 0.05,
            'multiplier': 100,
            'chart_id': 'chart-0'
        }
        """
        try:
            symbol = data.get('symbol')
            timeframe = data.get('timeframe', '5m')
            bucket_size = float(data.get('bucket_size', 0.05))
            multiplier = int(data.get('multiplier', 100))
            chart_id = data.get('chart_id')
            
            if not symbol or not chart_id:
                emit('error', {'message': 'Missing symbol or chart_id'})
                return
            
            # Create room ID for this specific subscription
            room_id = f"{request.sid}_{chart_id}_{symbol}_{timeframe}_{bucket_size}_{multiplier}"
            
            # Join the room
            join_room(room_id)
            
            # Store subscription details
            subscriber_rooms[room_id] = {
                'symbol': symbol,
                'timeframe': timeframe,
                'bucket_size': bucket_size,
                'multiplier': multiplier,
                'chart_id': chart_id,
                'client_id': request.sid
            }
            
            # Start live feed if not already running for this symbol
            start_live_feed(symbol, timeframe, bucket_size, multiplier)
            
            print(f"Client {request.sid} subscribed to {symbol} for chart {chart_id}")
            emit('subscription_success', {
                'symbol': symbol,
                'chart_id': chart_id,
                'room_id': room_id
            })
            
        except Exception as e:
            print(f"Error in subscribe_symbol: {e}")
            emit('error', {'message': str(e)})
    
    @socketio.on('unsubscribe_symbol')
    def handle_unsubscribe_symbol(data):
        """
        Unsubscribe from a symbol
        Expected data: {
            'symbol': 'NSE:NIFTY25AUGFUT',
            'chart_id': 'chart-0'
        }
        """
        try:
            symbol = data.get('symbol')
            chart_id = data.get('chart_id')
            
            if not symbol or not chart_id:
                emit('error', {'message': 'Missing symbol or chart_id'})
                return
            
            # Find and remove the room
            rooms_to_remove = []
            for room_id, room_data in subscriber_rooms.items():
                if (room_data['client_id'] == request.sid and 
                    room_data['symbol'] == symbol and 
                    room_data['chart_id'] == chart_id):
                    rooms_to_remove.append(room_id)
            
            for room_id in rooms_to_remove:
                leave_room(room_id)
                del subscriber_rooms[room_id]
            
            # Check if we should stop the feed for this symbol
            stop_live_feed_if_no_subscribers(symbol)
            
            print(f"Client {request.sid} unsubscribed from {symbol} for chart {chart_id}")
            emit('unsubscription_success', {
                'symbol': symbol,
                'chart_id': chart_id
            })
            
        except Exception as e:
            print(f"Error in unsubscribe_symbol: {e}")
            emit('error', {'message': str(e)})
    
    def cleanup_client_subscriptions(client_id):
        """Clean up all subscriptions for a disconnected client"""
        rooms_to_remove = []
        symbols_to_check = set()
        
        for room_id, room_data in subscriber_rooms.items():
            if room_data['client_id'] == client_id:
                rooms_to_remove.append(room_id)
                symbols_to_check.add(room_data['symbol'])
        
        for room_id in rooms_to_remove:
            del subscriber_rooms[room_id]
        
        # Check if we should stop feeds for symbols with no subscribers
        for symbol in symbols_to_check:
            stop_live_feed_if_no_subscribers(symbol)
    
    def start_live_feed(symbol, timeframe, bucket_size, multiplier):
        """Start live data feed for a symbol if not already running"""
        if symbol in live_feeds:
            # Feed already running, just add to subscribers count
            live_feeds[symbol]['subscribers'] += 1
            return
        
        try:
            # Create new feed instance
            fyers_feed = FyersDataFeed()
            
            # Create a custom callback that processes data for each subscriber with their own parameters
            def live_data_callback(raw_message):
                if not raw_message:
                    return
                    
                # Process the raw message for each room with their specific parameters
                for room_id, room_data in subscriber_rooms.items():
                    if room_data['symbol'] == symbol:
                        try:
                            # Import processor here to avoid circular imports
                            from core.fyers.processor import process_live_data, initialize_continuity_context
                            
                            # Process data with room-specific parameters
                            room_timeframe = room_data['timeframe']
                            room_bucket_size = room_data['bucket_size']
                            room_multiplier = room_data['multiplier']
                            
                            processed_data = process_live_data(
                                raw_message, 
                                timeframe=room_timeframe, 
                                bucket_size=room_bucket_size, 
                                multiplier=room_multiplier,
                                ensure_continuity=True
                            )
                            
                            if processed_data:
                                socketio.emit('live_data_update', {
                                    'symbol': symbol,
                                    'chart_id': room_data['chart_id'],
                                    'data': processed_data,
                                    'timeframe': room_timeframe,
                                    'timestamp': time.time()
                                }, room=room_id)
                                
                        except Exception as e:
                            print(f"Error processing live data for room {room_id}: {e}")
            
            # Start the live feed in a separate thread
            feed_thread = threading.Thread(
                target=run_live_feed,
                args=(fyers_feed, symbol, live_data_callback),
                daemon=True
            )
            feed_thread.start()
            
            # Store feed info
            live_feeds[symbol] = {
                'feed_instance': fyers_feed,
                'subscribers': 1,
                'thread': feed_thread,
                'callback': live_data_callback
            }
            feed_threads[symbol] = feed_thread
            
            print(f"Started live feed for {symbol}")
            
        except Exception as e:
            print(f"Error starting live feed for {symbol}: {e}")
    
    def run_live_feed(fyers_feed, symbol, callback):
        """Run live feed with custom callback - now passes raw message data"""
        try:
            data_type = "SymbolUpdate"
            
            def onmessage(raw_message):
                """Pass raw message to callback for per-room processing"""
                try:
                    if raw_message and callback:
                        callback(raw_message)
                except Exception as e:
                    print(f"Error in live feed callback: {e}")
            
            def onerror(message):
                print("Live feed error:", message)
                socketio.emit('live_feed_error', {
                    'symbol': symbol,
                    'error': str(message)
                })
            
            def onclose(message):
                print("Live feed connection closed:", message)
                socketio.emit('live_feed_closed', {
                    'symbol': symbol,
                    'message': str(message)
                })
            
            def onopen():
                from fyers_apiv3.FyersWebsocket import data_ws
                live_data.subscribe(symbols=[symbol], data_type=data_type)
                live_data.keep_running()
            
            from fyers_apiv3.FyersWebsocket import data_ws
            live_data = data_ws.FyersDataSocket(
                access_token=fyers_feed.access_token,
                log_path="",
                litemode=False,
                write_to_file=False,
                reconnect=True,
                on_connect=onopen,
                on_close=onclose,
                on_error=onerror,
                on_message=onmessage,
            )
            live_data.connect()
            
        except Exception as e:
            print(f"Error in live feed thread for {symbol}: {e}")
    
    def stop_live_feed_if_no_subscribers(symbol):
        """Stop live feed if no active subscribers"""
        if symbol not in live_feeds:
            return
        
        # Check if any rooms are still subscribed to this symbol
        has_subscribers = any(
            room_data['symbol'] == symbol 
            for room_data in subscriber_rooms.values()
        )
        
        if not has_subscribers:
            # Stop the feed
            if symbol in live_feeds:
                feed_info = live_feeds[symbol]
                # The WebSocket will be closed when the thread ends
                del live_feeds[symbol]
            
            if symbol in feed_threads:
                del feed_threads[symbol]
            
            print(f"Stopped live feed for {symbol} - no active subscribers")
